<script lang="ts">
    import TwoStepForm from '../../lib/ui/TwoStepForm.svelte';
    import { Button, H1, H2, H3, P1, P3 } from '$lib/ui';
	import { innerWidth } from 'svelte/reactivity/window';

    let { data } = $props();

    // Countdown timer state
    const targetDate = new Date('2025-09-12T23:59:59').getTime();
    let currentTime = $state(Date.now());
    let timeLeft = $derived(Math.max(0, targetDate - currentTime));

    // Calculate time units
    let days = $derived(Math.floor(timeLeft / (1000 * 60 * 60 * 24)));
    let hours = $derived(Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)));
    let minutes = $derived(Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60)));
    let seconds = $derived(Math.floor((timeLeft % (1000 * 60)) / 1000));

    // Update timer every second
    $effect(() => {
        const interval = setInterval(() => {
            currentTime = Date.now();
        }, 1000);

        return () => clearInterval(interval);
    });
</script>

<svelte:head>
	<title>DSAT16 - Special Offer!</title>
</svelte:head>

<div class="webpage">
    <!-- Header Section -->
    <div class="header-section">
        <H1>DSAT16 Offer Đặc Biệt!</H1>
        <div class="limited-time">
            <H2>Dành riêng cho người tham gia webinar</H2>
        </div>
        <div class="countdown-timer">
            <div class="countdown-display">
                <div class="time-unit">
                    <P1 isBold --text-color="var(--purple)">{days}</P1>
                    <P3>Ngày</P3>
                </div>
                <div class="time-separator">:</div>
                <div class="time-unit">
                    <P1 isBold --text-color="var(--purple)">{hours.toString().padStart(2, '0')}</P1>
                    <P3>Giờ</P3>
                </div>
                <div class="time-separator">:</div>
                <div class="time-unit">
                    <P1 isBold --text-color="var(--purple)">{minutes.toString().padStart(2, '0')}</P1>
                    <P3>Phút</P3>
                </div>
                <div class="time-separator">:</div>
                <div class="time-unit">
                    <P1 isBold --text-color="var(--purple)">{seconds.toString().padStart(2, '0')}</P1>
                    <P3>Giây</P3>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="form-and-table">
            <!-- Left Side - Order Form -->
            <div id="order-form" class="order-form">
                <TwoStepForm {data} />
            </div>

            <!-- Right Side - Video -->
            <div class="video">
                <div style="
                    width: 100%;
                    aspect-ratio: 16 / 9;
                    background: linear-gradient(135deg, var(--light-purple) 0%, var(--sky-blue) 100%);
                    border: 0.25rem solid var(--pitch-black);
                    border-radius: 1rem;
                    box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                    overflow: hidden;
                ">
                    <!-- Play button icon -->
                    <div role="button" tabindex="0" style="
                        width: 80px;
                        height: 80px;
                        background: var(--white);
                        border: 0.25rem solid var(--pitch-black);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
                        cursor: pointer;
                        transition: transform 0.2s ease;
                        margin-bottom: 1rem;
                    "
                    onmouseenter={(e) => e.currentTarget.style.transform = 'scale(1.1)'}
                    onmouseleave={(e) => e.currentTarget.style.transform = 'scale(1)'}>
                        <!-- Triangle play icon -->
                        <div style="
                            width: 0;
                            height: 0;
                            border-left: 20px solid var(--purple);
                            border-top: 12px solid transparent;
                            border-bottom: 12px solid transparent;
                            margin-left: 4px;
                        "></div>
                    </div>
                </div>
                {#if innerWidth.current as number > 1040}
                    {@render productTable()}
                {/if}
            </div>
        </div>
        
        <div class="product-info">
            {#if innerWidth.current as number <= 1040}
                {@render productTable()}
            {/if}
            <hr />
            {@render valueStack()}
        </div>
    </div>
</div>

{#snippet productTable()}
    <div class="product-table">
        <H3 --text-align="center">Tất cả mọi thứ trong gói</H3>
        <div class="table-container">
            <div class="table-row">
                <P1>DSAT16 (1 năm)</P1>
                <P1 --text-color="var(--rose)">Trị giá 4.992.000đ</P1>
            </div>
            <div class="table-row">
                <P1>DSAT16 Bootcamp (1 năm)</P1>
                <P1 --text-color="var(--rose)">Trị giá 1.160.000đ</P1>
            </div>
            <div class="table-row">
                <P1>Meeting với Tutor 1600</P1>
                <P1 --text-color="var(--rose)">Trị giá 5.616.000đ</P1>
            </div>
            <div class="table-row">
                <P1>Bonus: 67 đề thi SAT official</P1>
                <P1 --text-color="var(--rose)">Vô giá!</P1>
            </div>
            <div class="table-row">
                <P1>14 ngày hoàn tiền 100%</P1>
                <P1 --text-color="var(--rose)">Không rủi ro!</P1>
            </div>
        </div>
        <H3 --text-align="center">Tổng giá trị: <s style:color=var(--rose)>12.768.000đ</s></H3>
        <H3 --text-align="center">Mua trong hôm nay: <span style:color=var(--purple)>5.160.000đ</span></H3>
    </div>
{/snippet}

{#snippet valueStack()}
    <div class="value-stack">
        <H2 --text-align="center">Tất cả mọi thứ bạn sẽ được truy cập ngay lập tức nếu mua luôn trong hôm nay</H2>

        <div class="value-items">
            <div class="value-item">
                <H3>DSAT16 - Nền tảng tự học SAT (1 năm)</H3>
                <P1>Question Bank gồm 5000+ câu hỏi với giải thích chi tiết</P1>
                <P1>Tool học vocab giúp học từ vựng một cách tối ưu và hiệu quả</P1>
                <P1>Hệ thống Gamify để giúp bạn luôn có động lực và không burn out</P1>
                <P1 isBold --text-color="var(--purple)">Trị giá 4.992.000đ</P1>
            </div>

            <div class="value-item">
                <H3>DSAT16 Bootcamp (1 năm)</H3>
                <P1>Khóa học bao gồm:</P1>
                <P1>📽️ 11 video phân tích từng dạng câu hỏi</P1>
                <P1>📚 Ghi chú bài giảng hoàn toàn bằng tiếng Anh giúp bạn rèn tư duy tiếng Anh</P1>
                <P1>🔑 Chữa bài tập chi tiết</P1>
                <P1>🗂️ Các tài liệu học thêm giúp bạn cải thiện khả năng đọc hiểu văn phong học thuật chuyên sâu của Digital SAT</P1>
                <P1 isBold --text-color="var(--purple)">Trị giá 1.160.000đ</P1>
            </div>

            <div class="value-item">
                <H3>Meeting với Tutor 1600</H3>
                <P1>Gặp mặt với Phúc 2 tuần 1 buổi cho đến lúc thi</P1>
                <P1>Update tiến độ học tập, đảm bảo học sinh vẫn theo đúng lộ trình</P1>
                <P1>Phân tích chi tiết điểm mạnh, điểm yếu và lộ trình học tập cá nhân</P1>
                <P1>Giải đáp thắc mắc và chia sẻ kinh nghiệm thi thực tế</P1>
                <P1 isBold --text-color="var(--purple)">Trị giá 5.616.000đ</P1>
            </div>

            <div class="value-item">
                <H3>Bonus: 67 đề thi SAT official + Đảm bảo hoàn tiền</H3>
                <P1>Bộ sưu tập đầy đủ các đề thi SAT chính thức từ College Board</P1>
                <P1 isBold --text-color="var(--purple)">Vô giá!</P1>
            </div>
        </div>

        <div class="total-value">
            <H3>Tổng giá trị: <s style:color=var(--rose)>12.768.000đ</s></H3>
            <H2>Nếu mua ngay: <span style:color=var(--purple)>5.160.000đ</span></H2>
            <a href="#order-form">
                <Button fullWidth>Đăng ký ngay</Button>
            </a>
        </div>
    </div>
{/snippet}

<style>
    .webpage {
        width: 100%;
        font-size: 1rem;
        -webkit-tap-highlight-color: transparent;
        background: var(--white);
        min-height: 100vh;
    }

    .video {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .header-section {
        text-align: center;
        padding: 2rem;
        background: var(--sky-blue);
        border-bottom: 0.25rem solid var(--pitch-black);
    }

    .limited-time {
        margin-top: 1rem;
        padding: 1rem;
        background: var(--yellow);
        border: 0.125rem solid var(--pitch-black);
        border-radius: 0.5rem;
        display: inline-block;
    }

    .countdown-timer {
        margin-top: 1rem;
        text-align: center;
    }

    .countdown-display {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 0.5rem;
        flex-wrap: wrap;
    }

    .time-unit {
        display: flex;
        flex-direction: column;
        align-items: center;
        background: var(--white);
        border: 0.125rem solid var(--pitch-black);
        border-radius: 0.5rem;
        padding: 0.5rem;
        min-width: 3rem;
        box-shadow: 0.125rem 0.125rem 0 var(--pitch-black);
    }

    .time-separator {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--pitch-black);
        margin: 0 0.25rem;
    }

    .main-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        max-width: 90rem;
        margin: 0 auto;
        padding: 2rem;
    }

    .form-and-table {
        flex: 1;
        display: flex;
        gap: 2rem;
    }

    .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .order-form {
        background: var(--light-aquamarine);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
        height: fit-content;
        flex: 1 1 0;
    }

    .video {
        flex: 2 1 0;
    }

    .product-table {
        background: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        height: fit-content;
        text-wrap: balance;
    }

    .table-container {
        margin: 1rem 0;
        border: 3px solid var(--pitch-black);
    }

    .table-row {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem;
        border-bottom: 0.125rem solid var(--pitch-black);
        background: var(--very-light-sky-blue);
    }

    .table-row:last-child {
        border-bottom: none;
        border-radius: 0 0 0.5rem 0.5rem;
    }

    .value-stack {
        background: var(--light-purple);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        max-width: 60rem;
        margin: 0 auto;
    }

    .value-items {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .value-item {
        background: var(--white);
        border: 0.125rem solid var(--pitch-black);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0.125rem 0.125rem 0 var(--pitch-black);
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .total-value {
        text-align: center;
        background: var(--yellow);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
        .header-section {
            padding: 2rem 1rem;
        }

        .main-content {
            padding: 1rem;
        }

        .countdown-display {
            gap: 0.25rem;
        }

        .time-unit {
            min-width: 2.5rem;
            padding: 0.375rem;
        }

        :global(.table-row p:last-child) {
            text-align: end;
        }
    }

    @media (max-width: 1040px) {
        .form-and-table {
            flex-direction: column;
        }
    }
</style>