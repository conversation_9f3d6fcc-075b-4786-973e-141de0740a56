{"name": "dsat16-webinar", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.38.8", "svelte-check": "^4.0.0", "svelte-turnstile": "^0.11.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^7.0.4", "vite-plugin-devtools-json": "^1.0.0"}, "dependencies": {"@mailerlite/mailerlite-nodejs": "^1.4.1", "@sveltejs/enhanced-img": "^0.8.1", "card-validator": "^10.0.3", "cookie": "^1.0.2", "firebase": "^12.1.0", "posthog-js": "^1.260.2", "sveltekit-superforms": "^2.27.1", "zod": "^4.0.0"}}